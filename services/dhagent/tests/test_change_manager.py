"""Unit tests for ChangeManager."""

from src.usecases.change_management.state import TodoItem, TodoStatus


class TestTodoItem:
    """Test cases for the TodoItem model."""

    def test_todo_item_creation_with_defaults(self):
        """Test creating a TodoItem with default status."""
        todo = TodoItem(description="Test task")

        assert todo.description == "Test task"
        assert todo.status == TodoStatus.PENDING

    def test_todo_item_creation_with_custom_status(self):
        """Test creating a TodoItem with custom status."""
        todo = TodoItem(description="Completed task", status=TodoStatus.COMPLETED)

        assert todo.description == "Completed task"
        assert todo.status == TodoStatus.COMPLETED

    def test_todo_item_with_empty_description(self):
        """Test creating TodoItem with empty description."""
        todo = TodoItem(description="")

        assert todo.description == ""
        assert todo.status == TodoStatus.PENDING

    def test_todo_item_string_representation(self):
        """Test string representation of TodoItem."""
        todo = TodoItem(description="Test task", status=TodoStatus.IN_PROGRESS)

        todo_str = str(todo)
        assert "Test task" in todo_str
        assert "in_progress" in todo_str or "IN_PROGRESS" in todo_str


class TestTodoStatus:
    """Test cases for the TodoStatus enum."""

    def test_todo_status_values(self):
        """Test TodoStatus enum values."""
        assert TodoStatus.PENDING.value == "pending"
        assert TodoStatus.IN_PROGRESS.value == "in_progress"
        assert TodoStatus.COMPLETED.value == "completed"
        assert TodoStatus.FAILED.value == "failed"

    def test_todo_status_enum_members(self):
        """Test TodoStatus enum has all expected members."""
        expected_statuses = {"PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"}
        actual_statuses = {status.name for status in TodoStatus}
        assert actual_statuses == expected_statuses
