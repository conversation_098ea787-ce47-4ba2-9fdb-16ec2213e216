"""Unit tests for OAM entities."""

import json
from datetime import datetime
from pathlib import Path

import pytest
from pydantic import ValidationError

from src.external.licloud.cicd import Artifact, OAMAppDeployRunningSucceedEventData, OAMApplication, OAMComponent


class TestOAMApplication:
    """Test cases for the OAMApplication entity."""

    @pytest.fixture
    def sample_oam_data(self) -> dict:
        """Sample OAM application data for testing."""
        return {
            "appDesc": "提供数据制品、数据产品、模型计算、对接内外部各种系统的平台",
            "appName": "dave-lianshan2",
            "appNameCn": "连山数据驱动平台",
            "appStatus": 1,
            "createTime": "2023-03-25 14:03:18",
            "creator": "jialinjing",
            "id": 923,
            "owner": "jialinjing",
            "tenantId": "ptepyr",
            "updateTime": "2023-04-25 23:05:41",
        }

    def test_oam_application_deserialization(self, sample_oam_data):
        """Test deserializing OAMApplication from JSON data."""
        app = OAMApplication(**sample_oam_data)

        # Test basic fields
        assert app.app_desc == "提供数据制品、数据产品、模型计算、对接内外部各种系统的平台"
        assert app.app_name == "dave-lianshan2"
        assert app.app_name_cn == "连山数据驱动平台"
        assert app.app_status == 1
        assert app.creator == "jialinjing"
        assert app.id == 923
        assert app.owner == "jialinjing"
        assert app.tenant_id == "ptepyr"

    def test_oam_application_datetime_parsing(self, sample_oam_data):
        """Test that datetime fields are properly parsed."""
        app = OAMApplication(**sample_oam_data)

        # Test datetime fields
        assert isinstance(app.create_time, datetime)
        assert isinstance(app.update_time, datetime)

        assert app.create_time == datetime(2023, 3, 25, 14, 3, 18)
        assert app.update_time == datetime(2023, 4, 25, 23, 5, 41)

    def test_oam_application_from_json_string(self, sample_oam_data):
        """Test deserializing OAMApplication from JSON string."""
        json_string = json.dumps(sample_oam_data)
        parsed_data = json.loads(json_string)
        app = OAMApplication(**parsed_data)

        assert app.app_name == "dave-lianshan2"
        assert app.id == 923
        assert isinstance(app.create_time, datetime)

    def test_oam_application_serialization(self, sample_oam_data):
        """Test serializing OAMApplication back to dict."""
        app = OAMApplication(**sample_oam_data)
        serialized = app.model_dump(by_alias=True)

        # Check that aliases are used in serialization
        assert "appDesc" in serialized
        assert "appName" in serialized
        assert "appNameCn" in serialized
        assert "appStatus" in serialized
        assert "createTime" in serialized
        assert "tenantId" in serialized
        assert "updateTime" in serialized

        # Check values
        assert serialized["appDesc"] == sample_oam_data["appDesc"]
        assert serialized["appName"] == sample_oam_data["appName"]
        assert serialized["id"] == sample_oam_data["id"]

    def test_oam_application_invalid_datetime(self):
        """Test that invalid datetime strings raise ValidationError."""
        invalid_data = {
            "appDesc": "Test app",
            "appName": "test-app",
            "appNameCn": "测试应用",
            "appStatus": 1,
            "createTime": "invalid-date",  # Invalid datetime
            "creator": "test-user",
            "id": 123,
            "owner": "test-user",
            "tenantId": "test-tenant",
            "updateTime": "2023-04-25 23:05:41",
        }

        with pytest.raises(ValidationError) as exc_info:
            OAMApplication(**invalid_data)

        # Check that the error is related to datetime parsing
        errors = exc_info.value.errors()
        assert len(errors) > 0
        assert any("createTime" in str(error) or "create_time" in str(error) for error in errors)

    def test_oam_application_missing_required_fields(self):
        """Test that missing required fields raise ValidationError."""
        incomplete_data = {
            "appDesc": "Test app",
            "appName": "test-app",
            # Missing other required fields like appNameCn, appStatus, etc.
        }

        with pytest.raises(ValidationError) as exc_info:
            OAMApplication(**incomplete_data)

        errors = exc_info.value.errors()
        assert len(errors) > 0

    def test_oam_application_extra_fields_ignored(self, sample_oam_data):
        """Test that extra fields are handled according to Pydantic config."""
        data_with_extra = sample_oam_data.copy()
        data_with_extra["extraField"] = "should be ignored"

        # Should not raise an error (Pydantic ignores extra fields by default)
        app = OAMApplication(**data_with_extra)
        assert app.app_name == "dave-lianshan2"

    def test_oam_application_field_aliases(self, sample_oam_data):
        """Test that field aliases work correctly."""
        app = OAMApplication(**sample_oam_data)

        # Test that we can access fields by their Python names
        assert hasattr(app, "app_desc")
        assert hasattr(app, "app_name")
        assert hasattr(app, "app_name_cn")
        assert hasattr(app, "app_status")
        assert hasattr(app, "create_time")
        assert hasattr(app, "tenant_id")
        assert hasattr(app, "update_time")

        # Test values through Python field names
        assert app.app_desc == sample_oam_data["appDesc"]
        assert app.app_name == sample_oam_data["appName"]
        assert app.tenant_id == sample_oam_data["tenantId"]


class TestOAMComponent:
    """Test cases for the OAMComponent entity."""

    def test_oam_component_creation(self):
        """Test creating an OAMComponent with basic properties."""

        artifact = Artifact(
            appId=1,
            artifact="artifact-1",
            branch="main",
            commitId="commit-1",
            commitMsg="init commit",
            componentId=1,
            createTime=datetime.now(),
            creator="tester",
            committer="tester",  # 新增必填字段
            fromWorkflowName="workflow-1",
            id=1,
            image="image-1",
            imageCreateTime=datetime.now(),
            imageCreator="tester",
            imageTag="v1.0",
            isChosen=True,
            isDeleted=False,
            pipelineId=1,
            prodImage="prod-image-1",
            updateTime=datetime.now(),
            version="1.0",
        )
        component = OAMComponent(
            appId=1,
            artifact=artifact,
            artifactKey="key-1",
            classify=None,
            componentDesc="desc",
            componentLevel=1,
            componentName="comp-1",
            componentNameCn="组件一",
            componentStatus=1,
            componentType="type-1",
            componentTypeName=None,
            createTime=datetime.now(),
            creator="tester",
            deployType=None,
            id=123,
            language="python",
            updateTime=datetime.now(),
            vehicleCost=100,
        )
        assert component.id == 123
        assert component.component_name == "comp-1"

    def test_oam_component_missing_id(self):
        """Test that missing id field raises ValidationError."""
        with pytest.raises(ValidationError) as exc_info:
            OAMComponent()  # type: ignore

        errors = exc_info.value.errors()
        assert len(errors) > 0
        assert any("id" in str(error) for error in errors)


class TestOAMAppRunningDataSuccess:
    """Test cases for the app deploy running data success."""

    def test_oam_response_parse(self):
        # 读取 JSON 文件（用你附件里的数据）
        json_path = Path(__file__).parent / "fixtures" / "oam_app_deploy_running_succeed.json"
        with open(json_path, encoding="utf-8") as f:
            data = json.load(f)

        # 解析 JSON
        resp = OAMAppDeployRunningSucceedEventData.parse_obj(data)

        # 基础字段验证
        assert resp.operator == "zhouzheng5"
        assert resp.data.app.app_name == "observability-cost"
        assert resp.data.app.id == 4481
        assert resp.data.components[0].artifact.artifact.startswith("observability-cost-ar")
        assert resp.data.history.env == "dev"

        # traits 字段验证（不区分类型，直接验证数据字典）
        traits = resp.data.history.data.deploy_param[0].traits
        assert any(trait.trait_name == "log" for trait in traits)
        log_trait = next(t for t in traits if t.trait_name == "log")
        assert "logPath" in log_trait.trait_data
        assert log_trait.trait_data["enable"] is True

        # 遍历所有 traits 确保数据是 dict
        for trait in traits:
            assert isinstance(trait.trait_data, dict)
            assert "enable" in trait.trait_data

    def test_oam_round_trip(self):
        """测试 OAMAppDeployRunningSucceed 解析后再序列化是否保留结构"""
        json_path = Path(__file__).parent / "fixtures" / "oam_app_deploy_running_succeed.json"
        data = json.loads(json_path.read_text(encoding="utf-8"))

        resp = OAMAppDeployRunningSucceedEventData.parse_obj(data)
        new_data = resp.dict(by_alias=True)

        # 基本 key 一致
        assert set(new_data.keys()) == set(data.keys())
        # 操作者字段一致
        assert new_data["operator"] == data["operator"]
