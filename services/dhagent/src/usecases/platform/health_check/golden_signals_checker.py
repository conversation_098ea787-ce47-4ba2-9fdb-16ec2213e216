"""
Golden Signals Health Checker

This module provides high-level health assessment tools based on Google SRE's Four Golden Signals:
1. Latency - How long it takes to service a request
2. Traffic - How many requests are hitting your system
3. Errors - Rate of requests that are failing
4. Saturation - How "full" your service is (resource utilization)

This is a higher-level abstraction over the platform tools for AI agents to easily assess
component health without needing to understand low-level metrics queries.

## Design Philosophy

### Intelligent Time Series Compression
Instead of naive averaging, this module uses sophisticated statistical compression to preserve
critical information from thousands of data points (e.g., 1,800 samples over 30 minutes):

- **Multi-dimensional analysis**: Provides average, percentiles (P50, P95, P99), min/max,
  standard deviation, trend slope, and current value
- **Signal-specific aggregation**: Each golden signal uses optimal compression methods:
  * Latency: P95 primary (tail latency), Average secondary
  * Traffic: Average primary (load), Max for spike detection
  * Errors: P95 primary (error bursts), Average for baseline
  * Saturation: P95 primary (resource spikes), with trend analysis
- **Anomaly preservation**: Spikes, outliers, and variability aren't lost in compression

### Enhanced Trend Detection
Trends are calculated using:
1. Linear regression slope on time series data with signal-appropriate thresholds
2. Fallback to baseline comparison when insufficient data
3. Directional analysis (improving/stable/degrading/unknown)

### Example Usage

```python
# Simple AI Agent Usage
request = CheckGoldenSignalsRequest(
    component="user-service",
    env="prod",
    check_duration_minutes=30,       # Analyzes last 30 minutes
    baseline_duration_minutes=1440   # Compares against 24h ago (1440min)
)

# Deployment Analysis Usage
request = CheckGoldenSignalsRequest(
    component="user-service",
    env="prod",
    check_duration_minutes=5,        # Analyzes 5 minutes after deployment
    baseline_duration_minutes=10     # Compares against 5min before deployment (10min ago)
)

report = check_golden_signals(request)

# High-level assessment
if report.overall_status == HealthStatus.CRITICAL:
    print(f"🚨 Component health: {report.overall_score:.1f}/100")
    for issue in report.key_issues:
        print(f"   Issue: {issue}")
    for rec in report.recommendations:
        print(f"   Action: {rec}")

# Detailed signal analysis
latency = report.latency_health
print(f"Latency trend: {latency.trend}")
print(f"P95 response time: {latency.metrics[0].current_value:.1f}ms")

# Rich statistical context
p95_metric = latency.metrics[0]  # response_time_p95
summary = p95_metric.current_summary
print(f"Latency stats over {summary.raw_data_points} samples:")
print(f"  Average: {summary.average:.1f}ms")
print(f"  P95: {summary.p95:.1f}ms (SLA critical)")
print(f"  Max spike: {summary.max_value:.1f}ms")
print(f"  Variability: {summary.std_dev:.1f}ms std dev")
print(f"  Trend: {summary.trend_slope:.2f}ms/minute")
```

### Statistical Insights Examples

The enhanced compression provides AI agents with rich context:

```python
# Instead of: "Average latency = 150ms"
# AI gets comprehensive analysis:
MetricSummary(
    raw_data_points=1800,
    average=150.0,      # Overall performance
    p95=250.0,          # Tail latency (SLA critical)
    p99=400.0,          # Worst-case performance
    max_value=800.0,    # Absolute worst spike
    std_dev=45.0,       # Variability indicator
    trend_slope=2.5,    # Degrading 2.5ms/minute
    last_value=180.0    # Current state
)

# Enables intelligent insights:
# - "High latency variability detected (std dev: 45ms)"
# - "Latency spikes observed: maximum was 800ms"
# - "Latency trending upward at 2.5ms per minute"
# - "P95 latency exceeds SLA threshold of 200ms"
```

### Confidence Assessment

The system calculates confidence scores based on:
- Data availability (missing baseline reduces confidence)
- Trend analysis quality (unknown trends reduce confidence)
- Statistical consistency across signals

### AI-Friendly Features

- **Single function interface**: `check_golden_signals()` for complete assessment
- **Structured insights**: Machine-readable analysis with human-friendly messages
- **Actionable recommendations**: Concrete next steps for remediation
- **Adaptive thresholds**: Uses historical baselines when available
- **Weighted scoring**: Errors and latency prioritized over traffic and saturation

This design enables AI agents to make sophisticated health assessments without needing
to understand individual metrics, statistical analysis, or threshold management.
"""

from datetime import datetime
from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class GoldenSignal(str, Enum):
    """The four golden signals from Google SRE"""

    LATENCY = "latency"
    TRAFFIC = "traffic"
    ERRORS = "errors"
    SATURATION = "saturation"


class HealthStatus(str, Enum):
    """Health status levels"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class TimeSeriesAggregation(str, Enum):
    """Methods for aggregating time series data"""

    AVERAGE = "average"
    PERCENTILE_50 = "p50"
    PERCENTILE_95 = "p95"
    PERCENTILE_99 = "p99"
    MAX = "max"
    MIN = "min"
    LAST = "last"  # Most recent value
    RATE = "rate"  # Rate of change


class MetricSummary(BaseModel):
    """Statistical summary of time series data"""

    raw_data_points: int = Field(..., description="Number of raw data points")
    average: float = Field(..., description="Average value")
    p50: float = Field(..., description="50th percentile (median)")
    p95: float = Field(..., description="95th percentile")
    p99: float = Field(..., description="99th percentile")
    max_value: float = Field(..., description="Maximum value")
    min_value: float = Field(..., description="Minimum value")
    std_dev: float = Field(..., description="Standard deviation")
    last_value: float = Field(..., description="Most recent value")
    trend_slope: float | None = Field(None, description="Linear trend slope (change per minute)")


class SignalMetric(BaseModel):
    """A metric measurement for a golden signal"""

    signal: GoldenSignal = Field(..., description="Which golden signal this metric represents")
    metric_name: str = Field(..., description="Name of the underlying metric")
    current_summary: MetricSummary = Field(..., description="Statistical summary of current period")
    baseline_summary: MetricSummary | None = Field(None, description="Statistical summary of baseline period")
    primary_aggregation: TimeSeriesAggregation = Field(
        ..., description="Primary aggregation method used for health assessment"
    )
    current_value: float = Field(..., description="Primary aggregated value for current period")
    baseline_value: float | None = Field(None, description="Primary aggregated value for baseline period")
    unit: str = Field(..., description="Unit of measurement")
    threshold_warning: float | None = Field(None, description="Warning threshold")
    threshold_critical: float | None = Field(None, description="Critical threshold")


class SignalHealth(BaseModel):
    """Health assessment for a golden signal"""

    signal: GoldenSignal = Field(..., description="Which golden signal")
    status: HealthStatus = Field(..., description="Overall health status")
    score: float = Field(..., description="Health score (0-100, higher is better)")
    metrics: list[SignalMetric] = Field(..., description="Underlying metrics")
    insights: list[str] = Field(default_factory=list, description="Analysis insights and recommendations")
    trend: Literal["improving", "stable", "degrading", "unknown"] = Field(
        "unknown", description="Trend direction over time"
    )


class ComponentGoldenSignalsReport(BaseModel):
    """Comprehensive golden signals assessment report for a component"""

    component: str = Field(..., description="Component identifier")
    env: LiCloudEnv = Field(..., description="Environment")
    timestamp: datetime = Field(..., description="When the assessment was performed")
    overall_status: HealthStatus = Field(..., description="Overall component health")
    overall_score: float = Field(..., description="Overall health score (0-100)")

    # Individual signal assessments
    latency_health: SignalHealth = Field(..., description="Latency signal health")
    traffic_health: SignalHealth = Field(..., description="Traffic signal health")
    errors_health: SignalHealth = Field(..., description="Errors signal health")
    saturation_health: SignalHealth = Field(..., description="Saturation signal health")

    # Summary and recommendations
    key_issues: list[str] = Field(default_factory=list, description="Critical issues identified")
    recommendations: list[str] = Field(default_factory=list, description="Recommended actions")
    confidence: float = Field(..., description="Confidence in assessment (0-100)")


class CheckGoldenSignalsRequest(BaseModel):
    """Request to check golden signals for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    biz_cluster: str = Field(..., description="Business cluster identifier")
    reference_time: datetime = Field(
        default_factory=datetime.now,
        description="Reference time for calculations. Defaults to current time if not specified.",
    )
    duration_minutes: int = Field(default=30, description="Duration of each comparison period (minutes)", ge=1, le=1440)
    baseline_offset_minutes: int = Field(
        default=1440, description="How far back to look for baseline comparison (minutes)", ge=1, le=10080
    )
    include_insights: bool = Field(default=True, description="Include analysis insights")


def check_golden_signals(request: CheckGoldenSignalsRequest) -> ComponentGoldenSignalsReport:
    """
    Perform comprehensive golden signals health check for a component.

    This is the main interface for AI agents to assess component health without
    needing to understand individual metrics or thresholds.

    Timeline Analysis:
        The function compares two equal-duration periods for fair statistical comparison:

        ```
        # Example: Deployment analysis (5min check, 10min lookback):
        # |-- 5 min --|    [gap: 10 min]    |-- 5 min --|
        # baseline_start baseline_end        start_time   end_time
        #     ↑               ↑                 ↑           ↑
        # 20min ago       15min ago       5min ago      now

        # Example: Daily comparison (30min check, 24h lookback):
        # |-- 30 min --|    [gap: 24 hours]    |-- 30 min --|
        # baseline_start baseline_end           start_time   end_time
        #     ↑               ↑                     ↑           ↑
        # 25h ago         24h ago            30min ago      now

        # Both periods have identical duration for fair comparison
        ```

        Common scenarios:
        - Deployment analysis: check_duration_minutes=5, baseline_duration_minutes=10
          (Compare 5min after deployment vs 5min before deployment)
        - Hourly monitoring: check_duration_minutes=30, baseline_duration_minutes=60
          (Compare last 30min vs 30min from 1 hour ago)
        - Daily comparison: check_duration_minutes=30, baseline_duration_minutes=1440
          (Compare last 30min vs same time yesterday)
        - Weekly comparison: check_duration_minutes=60, baseline_duration_minutes=10080
          (Compare last hour vs same time last week)

    Args:
        request: Health check parameters

    Returns:
        Comprehensive golden signals assessment report
    """
    # Simplified mock implementation that always shows the critical latency scenario
    # This reflects the README scenario where deployment increases API latency

    # Always simulate the pricing service latency degradation scenario
    current_latency_p95 = 3000.0  # 3s - degraded after deployment
    baseline_latency_p95 = 200.0  # 200ms - normal before deployment
    latency_status = HealthStatus.CRITICAL
    latency_score = 15.0
    latency_insights = [
        "🚨 Critical latency degradation detected for API 'POST /api/v1/prices'",
        "P95 response time increased from 200ms to 3000ms (1400% increase)",
        "Latency spike coincides with recent deployment",
        "Immediate rollback recommended to restore service performance",
    ]
    trend: Literal["improving", "stable", "degrading", "unknown"] = "degrading"
    key_issues = ["API 'POST /api/v1/prices' latency increased 15x after deployment"]
    recommendations = [
        "Rollback the deployment immediately to restore performance",
        "Investigate the root cause after service recovery",
        "Review deployment changes that may have caused the latency spike",
    ]
    overall_status = HealthStatus.CRITICAL
    overall_score = 25.0

    # Build current period metrics
    current_latency_summary = MetricSummary(
        raw_data_points=request.duration_minutes * 60,  # 1 sample per second
        average=current_latency_p95 * 0.7,  # Average typically lower than P95
        p50=current_latency_p95 * 0.6,
        p95=current_latency_p95,
        p99=current_latency_p95 * 1.5,
        max_value=current_latency_p95 * 2.0,
        min_value=current_latency_p95 * 0.3,
        std_dev=current_latency_p95 * 0.2,
        last_value=current_latency_p95 * 0.9,
        trend_slope=2.5 if trend == "degrading" else (-1.0 if trend == "improving" else 0.1),
    )

    # Build baseline period metrics
    baseline_latency_summary = MetricSummary(
        raw_data_points=request.duration_minutes * 60,
        average=baseline_latency_p95 * 0.7,
        p50=baseline_latency_p95 * 0.6,
        p95=baseline_latency_p95,
        p99=baseline_latency_p95 * 1.5,
        max_value=baseline_latency_p95 * 2.0,
        min_value=baseline_latency_p95 * 0.3,
        std_dev=baseline_latency_p95 * 0.2,
        last_value=baseline_latency_p95 * 0.9,
        trend_slope=0.1,  # Stable baseline
    )

    # Create latency metric
    latency_metric = SignalMetric(
        signal=GoldenSignal.LATENCY,
        metric_name="response_time_p95",
        current_summary=current_latency_summary,
        baseline_summary=baseline_latency_summary,
        primary_aggregation=TimeSeriesAggregation.PERCENTILE_95,
        current_value=current_latency_p95,
        baseline_value=baseline_latency_p95,
        unit="ms",
        threshold_warning=500.0,
        threshold_critical=1000.0,
    )

    # Create latency health assessment
    latency_health = SignalHealth(
        signal=GoldenSignal.LATENCY,
        status=latency_status,
        score=latency_score,
        metrics=[latency_metric],
        insights=latency_insights,
        trend=trend,
    )

    # Create healthy mock data for other signals
    traffic_health = SignalHealth(
        signal=GoldenSignal.TRAFFIC,
        status=HealthStatus.HEALTHY,
        score=85.0,
        metrics=[],
        insights=["Traffic levels are normal"],
        trend="stable",
    )

    errors_health = SignalHealth(
        signal=GoldenSignal.ERRORS,
        status=HealthStatus.HEALTHY,
        score=90.0,
        metrics=[],
        insights=["Error rates are within acceptable limits"],
        trend="stable",
    )

    saturation_health = SignalHealth(
        signal=GoldenSignal.SATURATION,
        status=HealthStatus.HEALTHY,
        score=80.0,
        metrics=[],
        insights=["Resource utilization is healthy"],
        trend="stable",
    )

    return ComponentGoldenSignalsReport(
        component=request.component,
        env=request.env,
        timestamp=request.reference_time,
        overall_status=overall_status,
        overall_score=overall_score,
        latency_health=latency_health,
        traffic_health=traffic_health,
        errors_health=errors_health,
        saturation_health=saturation_health,
        key_issues=key_issues,
        recommendations=recommendations,
        confidence=95.0,
    )
