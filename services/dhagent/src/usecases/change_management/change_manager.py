import logging
import os
import uuid
from typing import TYPE_CHECKING, Any, cast

from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph, StateGraph
from langgraph.types import interrupt
from pydantic import BaseModel, Field

from src.config import settings
from src.usecases.shared import ComposableAgent

if TYPE_CHECKING:
    from src.external.licloud.cicd import History, OAMComponent

from src.usecases.change_management.state import (
    AgentMode,
    ChangeManagerState,
    TodoItem,
    TodoStatus,
)

from .prompt_templates import (
    complete_todo_prompt,
    format_component_description,
    format_deploy_history,
    format_todos,
    format_tool_descriptions,
    make_component_diagnostic_plan,
    mitigation_action_prompt,
)
from .tool_manager import ToolManager

logger = logging.getLogger(__name__)


class AgenticChangeManager(ComposableAgent):
    def __init__(self) -> None:
        self._chat_model = ChatOpenAI(
            model="azure-gpt-5-mini",
            base_url="http://api-hub.inner.chj.cloud/llm-gateway/v1",
            default_headers={
                "X-CHJ-GWToken": settings.apihub_token,
                "BCS-APIHub-RequestId": str(uuid.uuid1()),
            },
        )
        self._tool_manager = ToolManager()
        self._chat_model_with_diagnostic_tools = self._chat_model.bind_tools(
            self._tool_manager.list_tools(tag="diagnosis")
        )
        self._chat_model_with_mitigate_tools = self._chat_model.bind_tools(
            self._tool_manager.list_tools(tag="mitigation")
        )

        # 创建 checkpointer 用于状态持久化
        self._checkpointer = MemorySaver()
        self._graph: CompiledStateGraph = self._build_graph()

        if settings.debug:
            self._save_graph_visualization("change_manager_graph.png")

    def _build_graph(self) -> CompiledStateGraph:
        g = StateGraph(ChangeManagerState)

        # Add mode orchestrator
        g.add_node("orchestrate", self._orchestrate)

        # Add sub-graphs for each mode using LangGraph's native subgraph support
        g.add_node("diagnose", self._build_diagnosis_graph())
        g.add_node("mitigate", self._build_risk_mitigation_graph())
        g.add_node("verify", self._build_recovery_verification_graph())
        g.add_node("investigate", self._build_root_cause_analysis_graph())

        g.add_edge(START, "orchestrate")
        g.add_conditional_edges(
            "orchestrate",
            self._should_continue,
            {
                "diagnose": "diagnose",
                "mitigate": "mitigate",
                "verify": "verify",
                "investigate": "investigate",
                "END": END,
            },
        )

        # All sub-graphs return to orchestrator for mode switching decisions
        subgraphs = [
            "diagnose",
            "mitigate",
            "verify",
            "investigate",
        ]
        for subgraph in subgraphs:
            g.add_edge(subgraph, "orchestrate")

        return g.compile(checkpointer=self._checkpointer)

    async def _risk_mitigation_placeholder(self, state: ChangeManagerState) -> dict[str, Any]:
        """Placeholder for risk mitigation sub-graph"""
        return {
            "messages": [HumanMessage(content="Risk mitigation sub-graph not yet implemented")],
            "requested_mode": AgentMode.COMPLETION,
            "mode_change_reason": "Risk mitigation placeholder completed",
        }

    async def _mitigate_execute(self, state: ChangeManagerState) -> dict[str, Any]:
        """Execute mitigation action using LLM tool calling with HITL"""
        component: OAMComponent = state["component"]
        history: History = state["history"]

        # Get available mitigation tools
        available_tools = self._tool_manager.list_tools(tag="mitigation")
        tool_descriptions = format_tool_descriptions(available_tools, numbered=True)

        # Generate component and history descriptions
        component_description = format_component_description(component)
        deploy_history_description = format_deploy_history(history)

        # Create mitigation prompt with context
        prompt = mitigation_action_prompt.format(
            component_description=component_description,
            deploy_history_description=deploy_history_description,
            tool_descriptions=tool_descriptions,
        )

        # Get LLM response with bound mitigation tools
        ai_msg = cast("AIMessage", await self._chat_model_with_mitigate_tools.ainvoke(prompt))

        # Handle tool calls with interrupt (same pattern as diagnosis)
        result = []
        if hasattr(ai_msg, "tool_calls") and ai_msg.tool_calls:
            result = await self._handle_tool_calls_with_interrupt(ai_msg, state)
        else:
            # No tool calls, just return the AI message content
            result.append({"role": "assistant", "content": ai_msg.content})

        # Transition to verification after mitigation
        return {
            "messages": result,
            "requested_mode": AgentMode.RECOVERY_VERIFICATION,
            "mode_change_reason": "Mitigation action completed, moving to verification",
        }

    def _build_risk_mitigation_graph(self) -> CompiledStateGraph:
        """Build the risk mitigation sub-graph"""
        g = StateGraph(ChangeManagerState)
        g.add_node("mitigate_execute", self._mitigate_execute)
        g.add_edge(START, "mitigate_execute")
        g.add_edge("mitigate_execute", END)
        return g.compile(checkpointer=self._checkpointer)

    async def _recovery_verification_placeholder(self, state: ChangeManagerState) -> dict[str, Any]:
        """Placeholder for recovery verification sub-graph"""
        return {
            "messages": [HumanMessage(content="Recovery verification sub-graph not yet implemented")],
            "requested_mode": AgentMode.COMPLETION,
            "mode_change_reason": "Recovery verification placeholder completed",
        }

    def _build_recovery_verification_graph(self) -> CompiledStateGraph:
        """Build the recovery verification sub-graph"""
        # TODO: Implement recovery verification sub-graph
        g = StateGraph(ChangeManagerState)
        g.add_node("placeholder", self._recovery_verification_placeholder)
        g.add_edge(START, "placeholder")
        g.add_edge("placeholder", END)
        return g.compile(checkpointer=self._checkpointer)

    async def _root_cause_analysis_placeholder(self, state: ChangeManagerState) -> dict[str, Any]:
        """Placeholder for root cause analysis sub-graph"""
        return {
            "messages": [HumanMessage(content="Root cause analysis sub-graph not yet implemented")],
            "requested_mode": AgentMode.COMPLETION,
            "mode_change_reason": "Root cause analysis placeholder completed",
        }

    def _build_root_cause_analysis_graph(self) -> CompiledStateGraph:
        """Build the root cause analysis sub-graph"""
        # TODO: Implement root cause analysis sub-graph
        g = StateGraph(ChangeManagerState)
        g.add_node("placeholder", self._root_cause_analysis_placeholder)
        g.add_edge(START, "placeholder")
        g.add_edge("placeholder", END)
        return g.compile(checkpointer=self._checkpointer)

    def _build_diagnosis_graph(self) -> CompiledStateGraph:
        """Build the diagnosis sub-graph with plan, execute, and assess nodes"""
        g = StateGraph(ChangeManagerState)

        # Add diagnosis sub-graph nodes
        g.add_node("plan", self._diagnosis_plan)
        g.add_node("execute_next_todo", self._diagnosis_execute_todo)
        g.add_node("assess_risk", self._diagnosis_assess_risk)

        # Start with planning
        g.add_edge(START, "plan")

        # Plan leads to execution
        g.add_edge("plan", "execute_next_todo")

        # After executing todo, assess risk
        g.add_edge("execute_next_todo", "assess_risk")

        # Risk assessment decides next action
        g.add_conditional_edges(
            "assess_risk",
            self._diagnosis_route_next,
            {
                "continue_todos": "execute_next_todo",  # More todos to execute
                "request_mode_switching": END,  # Critical issues found
                "request_completion": END,  # All done, no issues
            },
        )

        return g.compile(checkpointer=self._checkpointer)

    @property
    def graph(self) -> CompiledStateGraph:
        return self._graph

    def _save_graph_visualization(self, output_path: str = "change_manager_graph.png") -> str:
        """
        Generate and save a PNG visualization of the graph structure.

        Args:
            output_path: Path where to save the PNG file (default: "change_manager_graph.png")

        Returns:
            The absolute path of the saved PNG file

        Raises:
            ImportError: If required visualization dependencies are not installed
            Exception: If graph visualization fails
        """
        try:
            # Get the PNG bytes from LangGraph's built-in visualization
            png_bytes = self._graph.get_graph(xray=True).draw_mermaid_png()

            # Save to file
            with open(output_path, "wb") as f:
                f.write(png_bytes)

            abs_path = os.path.abspath(output_path)
            logger.info(f"Graph visualization saved to: {abs_path}")
            return abs_path

        except ImportError as e:
            logger.error(f"Missing dependencies for graph visualization: {e}")
            logger.error("Please install: pip install pygraphviz or pip install 'langgraph[visualization]'")
            raise
        except Exception as e:
            logger.error(f"Failed to generate graph visualization: {e}")
            raise

    def _should_continue(self, state: ChangeManagerState) -> str:
        """Route based on current agent mode"""
        current_mode = state.get("current_mode", AgentMode.DIAGNOSIS)

        mode_to_subgraph = {
            AgentMode.DIAGNOSIS: "diagnose",
            AgentMode.RISK_MITIGATION: "mitigate",
            AgentMode.RECOVERY_VERIFICATION: "verify",
            AgentMode.ROOT_CAUSE_ANALYSIS: "investigate",
            AgentMode.COMPLETION: "END",
        }

        return mode_to_subgraph.get(current_mode, "END")

    async def _orchestrate(self, state: ChangeManagerState) -> dict[str, Any]:
        """
        Central orchestrator for mode switching and workflow coordination.

        Handles mode change requests from sub-graphs and determines the next operational mode.
        """
        # Check for mode change requests from sub-graphs
        requested_mode = state.get("requested_mode")

        if requested_mode and self._should_approve_mode_change(state, requested_mode):
            # Approve the mode change
            return {
                "current_mode": requested_mode,
                "requested_mode": None,  # Clear the request
                "mode_change_reason": None,  # Clear after processing
            }

        return {"current_mode": AgentMode.COMPLETION}

    def _should_approve_mode_change(self, state: ChangeManagerState, requested_mode: AgentMode) -> bool:
        """Validate if mode change request should be approved"""
        current_mode = state.get("current_mode")

        if not current_mode:
            return True

        # For now, approve all mode change requests
        # TODO: Add validation logic for mode transitions
        return requested_mode != current_mode

    async def _diagnosis_plan(self, state: ChangeManagerState) -> dict[str, Any]:
        """Dedicated node for creating or updating diagnostic plans"""

        class DiagnosticPlan(BaseModel):
            """Simple wrapper for diagnostic todos"""

            todos: list[TodoItem] = Field(..., description="List of investigation tasks to perform")

        component: OAMComponent = state["component"]
        history: History = state["history"]

        # Create a structured output model that knows about available tools
        planner = self._chat_model.with_structured_output(DiagnosticPlan)

        # Generate tool descriptions from available tools
        available_tools = self._tool_manager.list_tools()
        tool_descriptions = format_tool_descriptions(available_tools, numbered=False)

        # Generate component description
        component_description = format_component_description(component)

        # Generate deploy history description
        deploy_history_description = format_deploy_history(history)

        result = cast(
            "DiagnosticPlan",
            await planner.ainvoke(
                [
                    HumanMessage(
                        content=make_component_diagnostic_plan.format(
                            component_description=component_description,
                            deploy_history_description=deploy_history_description,
                            tool_descriptions=tool_descriptions,
                        )
                    )
                ]
            ),
        )

        return {
            "todos": result.todos,
            "next_todo_index": 0,
            "messages": [
                HumanMessage(
                    content=(
                        f"Operator just deployed an OAM Component {component.component_name}, "
                        f"now we need to help diagnose it according to the following plan:\n"
                        f"{format_todos(result.todos, 0)}"
                    )
                )
            ],
        }

    async def _diagnosis_execute_todo(self, state: ChangeManagerState) -> dict[str, Any]:
        """Execute next todo: LLM call + tool execution with HITL"""
        todos = state["todos"]
        next_todo_index = state.get("next_todo_index", 0)
        next_todo = todos[next_todo_index]

        # Generate tool descriptions from available tools
        available_tools = self._tool_manager.list_tools()
        tool_descriptions = format_tool_descriptions(available_tools, numbered=True)

        # Generate LLM response with tools
        prompt = complete_todo_prompt.format(todo=next_todo, tool_descriptions=tool_descriptions)
        ai_msg = cast("AIMessage", await self._chat_model_with_diagnostic_tools.ainvoke(prompt))

        # Immediately handle tool calls with interrupts
        result = []
        if hasattr(ai_msg, "tool_calls") and ai_msg.tool_calls:
            result = await self._handle_tool_calls_with_interrupt(ai_msg, state)
        else:
            # No tool calls, just return the AI message content
            result.append({"role": "assistant", "content": ai_msg.content})

        # Update todo status
        todos[next_todo_index].status = TodoStatus.COMPLETED

        return {
            "messages": result,
            "todos": todos,
            "next_todo_index": next_todo_index + 1,
        }

    async def _handle_tool_calls_with_interrupt(
        self, ai_message: AIMessage, state: ChangeManagerState
    ) -> list[dict[str, Any]]:
        """处理工具调用，支持中断逻辑"""

        result = []

        for tool_call in ai_message.tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]

            interruptible_tool = self._tool_manager.get_interruptible_tool(tool_name)
            if not interruptible_tool.is_sensitive:
                observation = interruptible_tool.tool.invoke(tool_args)
                result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
                continue

            # Create the interrupt request
            request = {
                "action_request": {"action": tool_call["name"], "args": tool_call["args"]},
                "config": interruptible_tool.config,
                "description": interruptible_tool.tool.description,
            }

            logger.info(f"HITL Tool call: {tool_call['name']} with args: {tool_call['args']}, request: {request}")

            response = interrupt([request])

            logger.info(f"User resume response: {response}, type: {response.type}, tool_call_id: {tool_call['id']}")

            # Handle the user response
            if response.type == "accept":
                # Execute the tool with original args
                observation = interruptible_tool.tool.invoke(tool_args)
                result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})

            elif response.type == "ignore":
                # Ignore this tool call and end the workflow
                result.append(
                    {
                        "role": "tool",
                        "content": f"User ignored {tool_name} and end the workflow.",
                        "tool_call_id": tool_call["id"],
                    }
                )
                state["next_todo_index"] = len(state["todos"])  # mark as completed
                break
            elif response.type == "edit":
                logger.info("implement edit logic here")
                pass
            elif response.type == "response":
                logger.info("implement response logic here")
                pass
            else:
                raise ValueError(f"Invalid tool call: {tool_call['name']}")

        return result

    async def _diagnosis_assess_risk(self, state: ChangeManagerState) -> dict[str, Any]:
        """
        Assess findings from diagnosis execution for risk escalation.

        This node analyzes recent tool outputs and messages to determine if critical issues were found.
        It is responsible for updating requested_mode and mode_change_reason when:
        - Critical issues are detected → request RISK_MITIGATION mode
        - All todos completed successfully → request COMPLETION mode
        - Continue normal flow → no mode change request

        Returns:
            dict with requested_mode and mode_change_reason if mode change needed, empty dict otherwise
        """
        # NOTE: this is just a mock, replace it with the real assessment logic @zhenghe
        return {
            "requested_mode": AgentMode.RISK_MITIGATION,
            "mode_change_reason": (
                "Critical issues detected during diagnosis, "
                "the latency of API 'POST /api/v1/prices' soars after deployment, "
                "the 95-percentile reaches 3s, compared to 200ms before the deployment."
            ),
        }

    def _diagnosis_route_next(self, state: ChangeManagerState) -> str:
        """Route diagnosis sub-graph based on assessment results"""

        # Check if mode switching is requested
        if state.get("requested_mode"):
            return "request_mode_switching"

        todos = state.get("todos", [])
        next_todo_index = state.get("next_todo_index", 0)

        # Check if there are more todos to execute
        if next_todo_index < len(todos):
            return "continue_todos"

        # All todos completed, request completion
        return "request_completion"
