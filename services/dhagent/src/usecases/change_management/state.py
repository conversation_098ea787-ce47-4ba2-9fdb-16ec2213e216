"""
State models and enums for the ChangeManager workflow.

This module contains all state-related Pydantic models and enums to avoid circular imports
and provide a clean separation of concerns.
"""

from enum import Enum

from langgraph.graph import MessagesState
from pydantic import BaseModel, Field

from src.external.licloud.cicd import History, OAMComponent


class TodoStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class AgentMode(str, Enum):
    """Dynamic operational modes for the ChangeManager workflow"""

    DIAGNOSIS = "diagnosis"
    RISK_MITIGATION = "risk_mitigation"
    RECOVERY_VERIFICATION = "recovery_verification"
    ROOT_CAUSE_ANALYSIS = "root_cause_analysis"
    COMPLETION = "completion"

    @classmethod
    def get_descriptions(cls) -> dict[str, str]:
        """Get mode descriptions for prompt generation"""
        return {
            cls.DIAGNOSIS: "Execute diagnostic todos and health checks (includes HITL for actions)",
            cls.RISK_MITIGATION: "Handle critical issues with immediate remediation actions",
            cls.RECOVERY_VERIFICATION: "Verify that remediation actions resolved the issues",
            cls.ROOT_CAUSE_ANALYSIS: "Deep dive analysis to understand why issues occurred",
            cls.COMPLETION: "All work completed successfully",
        }


class TodoItem(BaseModel):
    """Represents a single todo item in the analysis plan"""

    description: str = Field(..., description="Description of the task to be performed")
    status: TodoStatus = Field(
        default=TodoStatus.PENDING,
        description=("Todo status"),
    )


class ChangeManagerState(MessagesState):
    # Share states
    ## inputs
    component: OAMComponent
    history: History
    ## workflow tracking
    current_mode: AgentMode
    requested_mode: AgentMode | None
    mode_change_reason: str | None

    ## Diagnosis sub-graph states
    todos: list[TodoItem]  # plan to do list
    next_todo_index: int
