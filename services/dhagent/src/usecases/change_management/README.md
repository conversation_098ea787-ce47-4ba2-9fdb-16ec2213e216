# ChangeManager

ChangeManager helps developers mitigate the risk after deployment. Usually they have
to check all the relevant signals, like metrics, logs, traces, alerts, and other events,
and also check the states of downstream services, namely whether they are affected by
this. When spotting risks, ChangeManager:

1. suggest actions that stop the loss.
2. co-operate with developers to find the root-cause if possible.

## Scenarios

The following scenarios describe how ChangeManager work in real world.

### Scenario 1: New deployment increases the latency of an critical API

After receiving the deployment success event, ChangeManager starts the conversation:

```
ChangeManager:
We just found the you deployed the pricing service. Now I'm going to
help you check its health and impacts.

ChangeManager:
Here is my plan -
1. check the golden signals before and after the deployment;
2. check the log patterns before and after the deployment;
3. check the trace patterns before and after the deployment;
4. check the topology with error information to see the impacts after the deployment;

ChangeManager:
Based on the golden signals, the latency of API "POST /api/v1/prices" soars after deployment, the 95-percentile reaches 3s, compared to 200ms before the deployment.

ChangeManager:
You'd better rollback the deployment. Do you want me to rollback?

Developer:
Yes

ChangeManager:
Now the service rollbacked to its previous version, do you want me to check the health again?

Developer:
Yes

ChangeManager:
Let's check the golden signals again.

ChangeManager:
Now the latency of API "POST /api/v1/prices" has returned to its previous state.

ChangeManager:
Do you want me to check the traces to find the root cause?

Developer:
Yes

ChangeManager:
Based on these traces:
- https://some-tracing-service.com/traces/132456
- https://some-tracing-service.com/traces/876590
- https://some-tracing-service.com/traces/964368
The following SQL hit the database without correct indexes built:
SELECT * FROM users WHERE name = "zhangsan"
I suggest you add the following index:
ALTER TABLE users ADD INDEX idx_name (`name`);
```
