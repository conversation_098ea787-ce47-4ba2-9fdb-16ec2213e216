from langchain_core.tools import StructuredTool
from pydantic import BaseModel, Field

from src.usecases.platform.health_check import (
    check_golden_signals,
    check_logs,
    check_topology,
    check_traces,
)
from src.usecases.platform.tools import (
    rollback_component,
    search_events,
)


class InterruptConfig(BaseModel):
    allow_accept: bool = Field(default=True, description="是否允许接受")
    allow_ignore: bool = Field(default=True, description="是否允许忽略")
    allow_response: bool = Field(default=False, description="是否允许响应")
    allow_edit: bool = Field(default=False, description="是否允许编辑")


class InterruptibleTool(BaseModel):
    tool: StructuredTool
    tags: list[str] = Field(default_factory=list, description="工具标签")
    is_sensitive: bool = Field(default=False, description="是否为敏感工具")
    config: InterruptConfig | None = Field(default=None, description="中断配置")


class ToolManager:
    def __init__(self) -> None:
        self._tools = {
            "search_events": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=search_events,
                    name="search_events",
                    description=(
                        "Search for infrastructure and application events like deployments, "
                        "rollbacks, database changes, network modifications, etc. "
                        "Supports natural language queries."
                    ),
                ),
                tags=["diagnosis"],
                is_sensitive=False,
            ),
            "check_logs": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=check_logs,
                    name="check_logs",
                    description=(
                        "Perform comprehensive log health check for a component."
                        "This is the main interface for AI agents to assess component health "
                        "based on log patterns, error rates, and anomalies "
                        "without needing to understand low-level log queries."
                    ),
                ),
                tags=["diagnosis"],
                is_sensitive=False,
            ),
            "check_golden_signals": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=check_golden_signals,
                    name="check_golden_signals",
                    description=(
                        "Check the health of golden signals for a component, including "
                        "latency, traffic, errors, and saturation metrics."
                    ),
                ),
                tags=["diagnosis"],
                is_sensitive=False,
            ),
            "check_traces": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=check_traces,
                    name="check_traces",
                    description=(
                        "Check the health of traces for a component, including latency, error rates, and request paths."
                    ),
                ),
                tags=["diagnosis"],
                is_sensitive=False,
            ),
            "check_topology": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=check_topology,
                    name="check_topology",
                    description=(
                        "Check the health of the service topology for a component, including "
                        "service dependencies, communication paths, and potential bottlenecks."
                    ),
                ),
                tags=["diagnosis"],
                is_sensitive=False,
            ),
            "rollback_component": InterruptibleTool(
                tool=StructuredTool.from_function(
                    func=rollback_component,
                    name="rollback_component",
                    description=("Rollback an OAM component to a previous version using history ID."),
                ),
                tags=["mitigation"],
                is_sensitive=True,
                config=InterruptConfig(allow_accept=True, allow_ignore=True, allow_edit=True, allow_response=True),
            ),
        }

    def list_tools(self, tag: str | None = None) -> list[StructuredTool]:
        """List all available tools"""
        if not tag:
            return [tool.tool for tool in self._tools.values()]

        filtered = []
        for tool in self._tools.values():
            if tag in tool.tags:
                filtered.append(tool.tool)
        return filtered

    def get_tool_descriptions(self) -> dict[str, str]:
        """Get a mapping of tool names to their descriptions"""
        return {name: tool.tool.description for name, tool in self._tools.items()}

    def get_tool_info(self) -> dict[str, dict[str, str | bool]]:
        """Get detailed info about all tools including name and description"""
        return {
            name: {
                "name": tool.tool.name,
                "description": tool.tool.description,
                "is_sensitive": tool.is_sensitive,
            }
            for name, tool in self._tools.items()
        }

    def get_interruptible_tool(self, name: str) -> InterruptibleTool:
        return self._tools[name]
